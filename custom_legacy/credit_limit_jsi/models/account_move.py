from odoo import api, fields, models


class AccountMove(models.Model):
    _inherit = "account.move"

    amount_discounted = fields.Float(string="Discounted Amount", compute="_compute_amount_discounted")

    @api.depends("amount_total")
    def _compute_amount_discounted(self):
        for move in self:
            amount_undiscounted = 0.0
            for line in move.invoice_line_ids:
                amount_undiscounted += (
                    line.price_subtotal + line.price_unit * ((line.discount or 0.0) / 100.0) * line.quantity
                )
            move.amount_discounted = -(amount_undiscounted - move.amount_total)
