from odoo import api, fields, models


class StockMove(models.Model):
    _inherit = "stock.move"

    secondary_uom_qty = fields.Float(
        "Secondary qty", digits="Product Unit of Measure", compute="_compute_secondary_data"
    )
    secondary_uom_id = fields.Many2one("uom.uom", "Secondary uom", compute="_compute_secondary_data")

    @api.depends("quantity")
    def _compute_secondary_data(self):
        for move in self:
            move.secondary_uom_qty, move.secondary_uom_id = self.set_secondary_data(
                move.quantity, move.product_uom, move.product_id.secondary_uom_id
            )

    def set_secondary_data(self, quantity, product_uom, secondary_uom_id):
        if not secondary_uom_id:
            return 0.0, False
        if secondary_uom_id == product_uom:
            return quantity, product_uom
        else:
            secondary_uom_qty = product_uom._compute_quantity(
                quantity, secondary_uom_id, rounding_method="HALF-UP"
            )
            return secondary_uom_qty, secondary_uom_id.id


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    secondary_uom_qty = fields.Float(
        "Secondary qty", digits="Product Unit of Measure", compute="_compute_secondary_data"
    )
    secondary_uom_id = fields.Many2one("uom.uom", "Secondary uom", compute="_compute_secondary_data")

    @api.depends("quantity")
    def _compute_secondary_data(self):
        StockMove = self.env["stock.move"]
        for line in self:
            line.secondary_uom_qty, line.secondary_uom_id = StockMove.set_secondary_data(
                line.quantity, line.product_uom_id, line.product_id.secondary_uom_id
            )
