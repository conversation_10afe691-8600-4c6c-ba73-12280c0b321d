from datetime import datetime

from odoo import _, api, fields, models
from odoo.exceptions import UserError


class MoveSelvedge(models.Model):
    _name = "move.selvedge"
    _description = "Selvedge"

    name = fields.Char()
    description = fields.Char()


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    gross_unit = fields.Float("Gross")
    selvedge_id = fields.Many2one("move.selvedge")
    lot_qty_available = fields.Float(related="lot_id.product_qty", string="Available Quantity")
    display_warning = fields.Boolean("Display Warning", compute="_compute_display_warning", help="Transfer quantity is different from the LOT available quantity. Please DOUBLE check;")

    @api.depends("quantity", "lot_id")
    def _compute_display_warning(self):
        for record in self:
            display_warning = False
            if record.move_id.picking_code == "internal" and record.lot_id and record.quantity != record.lot_qty_available:
                display_warning = True
            record.display_warning = display_warning

    @api.constrains("gross_unit", "selvedge_id", "quantity")
    def _validate_gross_unit_selvedge(self):
        '''
            1) Only for Receipt and product with lot
            2) Check only from Detailed Operation window(Only when user MANUALLY change the quantity)
                - No need to check when move is creating from Backorder or from Purchase Order
                - This constraint will call when user creating receipt from Purchase form(active_model=po)
                - Same will called when backorder is creating and we dont want to check it in that case(context=button_validate_picking_ids)
                - No needed to check when user is creating return of receipt (self.picking_id.return_id)
        '''
        if self.env.context.get('active_model') == "stock.picking" and 'button_validate_picking_ids' not in self.env.context:
            for record in self:
                if not self.picking_id.return_id and\
                    record.move_id.picking_code == "incoming" and\
                        record.move_id.has_tracking != 'none' and\
                        record.quantity != 0:
                    if record.gross_unit <= 0:
                        raise UserError(_("Gross must be greater than 0.00"))
                    if not record.selvedge_id:
                        raise UserError(_("Selvedge value is missing."))

    @api.model_create_multi
    def create(self, vals_list):
        """OVERRIDE
            Only for Receipt and product with lot
            As required data is added after move is created, we will do the same: https://github.com/odoo/odoo/blob/17.0/addons/stock/models/stock_move_line.py#L315
        """
        move_lines = super(StockMoveLine, self).create(vals_list)
        sequence_obj = self.env["ir.sequence"]
        datetime_min = fields.Datetime.today()
        for line in move_lines:
            if line.picking_code == "incoming" and line.lots_visible:
                next_lot_name = sequence_obj.next_by_code("lot.name")
                formated_date = datetime.strftime(datetime_min, "%y%m%d")
                selvedge_name = line.selvedge_id.name if line.selvedge_id else ""
                # In vals_list, we dont have piking_id, so hard to checking our condition(picking_code and lots_visible)
                # If there is no selvedge or gross_unit, then quantity should be 0
                # And once quantity=0, odoo will force user to create backorder at the time of validation and this is what client want
                if not (line.selvedge_id or line.gross_unit):
                    line.quantity = 0
                lot_name = "%.2f/%.2f/%s/%s/%s" % (
                    line.quantity,
                    line.gross_unit,
                    selvedge_name,
                    formated_date,
                    next_lot_name,
                )
                line.lot_name = lot_name
        return move_lines

    def write(self, vals):
        # Only for Receipt and product with lot
        for ml in self:
            if (
                ml.lot_name
                and ml.move_id.picking_code == "incoming"
                and ml.move_id.show_lots_text
            ):
                if vals.get("quantity", vals.get("gross_unit", vals.get("selvedge_id"))):
                    splitted_name = ml.lot_name.split("/")
                    if "quantity" in vals:
                        splitted_name[0] = "%.2f" % vals["quantity"]
                    if "gross_unit" in vals:
                        splitted_name[1] = "%.2f" % vals["gross_unit"]
                    if "selvedge_id" in vals:
                        selvedge_obj = self.env["move.selvedge"]
                        selvedge_name = selvedge_obj.browse(vals["selvedge_id"]).name
                        splitted_name[2] = selvedge_name
                    vals["lot_name"] = "/".join(splitted_name)
        return super(StockMoveLine, self).write(vals)

    def _prepare_new_lot_vals(self):
        """ OVERRIDE
            passing gross_unit and setting auto_seq to new lot number"""
        res = super()._prepare_new_lot_vals()
        if self.picking_code == "incoming":
            res.update({
                "gross_unit": self.gross_unit,
                "auto_seq": True,
            })
        return res

    def _action_reset_date_on_lot_name(self):
        """Reset date on lot name"""
        for record in self:
            if record.move_id.picking_code == "incoming" and record.move_id.show_lots_text:
                splitted_name = record.lot_name.split("/")
                splitted_name[3] = datetime.strftime(fields.Datetime.today(), "%y%m%d")
                record.lot_name = "/".join(splitted_name)
        return True

    def _action_reset_quantity(self):
        """Reset Quantity to 0 """
        for record in self:
            if record.move_id.picking_code == "incoming" and record.lot_name:
                record.quantity = 0
        return True
