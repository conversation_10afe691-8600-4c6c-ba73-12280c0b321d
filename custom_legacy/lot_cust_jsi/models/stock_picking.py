from odoo import models


class StockPicking(models.Model):
    _inherit = "stock.picking"

    def button_validate(self):
        """Override
            - for handle stock.quants
            - Force set todays date on lot name when click on validate button
        """
        self.move_line_ids._action_reset_date_on_lot_name()
        res = super().button_validate()
        if res not in [True, None]:
            return res
        # There may be morethan one picking now, loop it
        for picking in self:
            if picking.picking_type_code == "internal":
                self.env["stock.quant"]._merge_quants()
                self.env["stock.quant"]._unlink_zero_quants()
                break
