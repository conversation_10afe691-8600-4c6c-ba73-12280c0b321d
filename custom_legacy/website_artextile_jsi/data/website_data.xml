<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Make custom fields whitelisted -->
    <function model="ir.model.fields" name="formbuilder_whitelist">
        <value>res.partner</value>
        <value eval="['contact', 'full_name', 'street2']"/>
    </function>
    <function model="ir.model.fields" name="formbuilder_whitelist">
        <value>sale.order</value>
        <value eval="['full_name']"/>
    </function>
    <function model="ir.model.fields" name="formbuilder_whitelist">
        <value>res.company</value>
        <value eval="['allowed_max_sale_qty']"/>
    </function>
</odoo>
