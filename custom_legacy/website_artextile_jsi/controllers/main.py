from odoo import http, _
from odoo.addons.website_sale.controllers.variant import WebsiteSaleVariantController
from odoo.addons.website.controllers import main
from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo.exceptions import UserError
from odoo.http import request
from odoo.tools import lazy


class Website(main.Website):

    @http.route()
    def autocomplete(self, search_type=None, term=None, order=None, limit=5, max_nb_chars=999, options=None):
        '''OVERRIDE
            - CATEGORY FILTER
            - Simple way to remove Pricing details in search panel
            - Remove category filter during typing product name in search bar
        '''
        if 'displayDetail' in options:
            options['displayDetail'] = False
        if 'category' in options:
            options['category'] = False
        return super().autocomplete(search_type, term, order, limit, max_nb_chars, options)


class WebsiteSale(WebsiteSale):

    def _get_additional_extra_shop_values(self, values, **post):
        """OVERRIDE
            - CATEGORY FILTER
            - If product found, odoo will display only its category, so remove it
            (This is hook to update values before rendering Product Listing Page, calling from `shop` method)
        """
        if values.get('search_categories_ids'):
            values['search_categories_ids'] = []
        if values.get('categories'):
            '''
                Display full list of category on Product Listing Page
                When we search product in search bar, odoo will display only its category
                So, we need to remove it as client want full list of category
                https://github.com/odoo/odoo/blob/17.0/addons/website_sale/controllers/main.py#L427
                (categs_domain.append(('id', 'in', search_categories.ids)))
            '''
            Category = request.env['product.public.category']
            website = request.env['website'].get_current_website()
            website_domain = website.website_domain()
            categs_domain = [('parent_id', '=', False)] + website_domain
            categs = lazy(lambda: Category.search(categs_domain))
            values['categories'] = categs
        return super()._get_additional_extra_shop_values(values, **post)

    @http.route()
    def shop(self, page=0, category=None, search='', min_price=0.0, max_price=0.0, ppg=False, **post):
        """OVERRIDE
            - CATEGORY FILTER
            - Search product without category filter
        """
        # If search term is there, remove category filter
        if search:
            category = False
        return super().shop(page=page, category=category, search=search, min_price=min_price, max_price=max_price, ppg=ppg, **post)

    @http.route()
    def cart(self, **post):
        """User must be loggedin to access Cart otherwise redirect to login page"""
        if request.website.is_public_user():
            return request.redirect("/web/login")
        return super(WebsiteSale, self).cart(**post)

    @http.route(
        ["/shop/cart/update_line_remark"], type="json", auth="public", methods=["POST"], website=True, csrf=False
    )
    def update_line_remark(self, product_id=None, line_id=None, line_remark=""):
        """
        For handle customer_po in cart line
        """
        order = request.website.sale_get_order()
        if order:
            order_line = order._cart_find_product_line(product_id=product_id, line_id=line_id)
            if order_line:
                order_line["customer_po"] = line_remark
        return {}

    def _shop_lookup_products(self, attrib_set, options, post, search, website):
        """
            Used in Product Listing Page to load Yard price
            Update context with UOM, It will used in `_compute_price_rule`
        """
        fuzzy_search_term, product_count, search_result = super()._shop_lookup_products(attrib_set=attrib_set, options=options, post=post, search=search, website=website)
        secondary_uom = request.env.ref("secondary_uom_jsi.product_uom_yard").id
        return fuzzy_search_term, product_count, search_result.with_context(selected_uom=secondary_uom)

    @http.route()
    def cart_update(
        self, product_id, add_qty=1, set_qty=0,
        product_custom_attribute_values=None, no_variant_attribute_values=None,
        express=False, **kwargs
    ):
        """
            This route is called when we press enter key in quantity input tag on Product Page
            - We need to pass selected UOM, used inside `_prepare_order_line_values` method
        """
        if kwargs.get('uom_id') not in [None, 'NaN', False]:
            kwargs['selected_uom'] = int(kwargs.get('uom_id'))

        return super().cart_update(
            product_id=product_id, add_qty=add_qty, set_qty=set_qty,
            product_custom_attribute_values=product_custom_attribute_values, no_variant_attribute_values=no_variant_attribute_values,
            express=express, **kwargs
        )

    @http.route()
    def address(self, **kw):
        """OVERRIDE
            Extra check point to restrict updation on shipping Address which are already used
        """
        # Need to check it only when user change address data and submit it
        # Ref: https://github.com/odoo/odoo/blob/14.0/addons/website_sale/controllers/main.py#L748
        if 'submitted' in kw and request.httprequest.method == "POST":
            order = request.website.sale_get_order()
            partner_id = int(kw.get('partner_id', -1))
            if partner_id != -1:
                used_shipping_ids = order.partner_id.sale_order_ids.filtered(lambda x: x.state not in ['draft', 'cencel']).partner_shipping_id.ids
                if partner_id in used_shipping_ids:
                    raise UserError(_("This Shipping Address is already used in Sale Order. You can't edit it."))
        return super().address(**kw)


class WebsiteSaleVariantControllerAR(WebsiteSaleVariantController):
    @http.route()
    def get_combination_info_website(
        self, product_template_id, product_id, combination, add_qty, parent_combination=None,
        **kwargs
    ):
        """OVERRIDE
        Add `selected_uom` in context, it will help in `_get_additionnal_combination_info`
        And from there context will pass to `_compute_price_rule`, it will help to convert price based on UOM on `Product Page`
        """
        selected_uom = kwargs.get('selected_uom')
        if selected_uom:
            selected_uom = request.env['uom.uom'].sudo().browse(int(kwargs.get('selected_uom')))
            request.update_context(selected_uom=selected_uom)
        return super().get_combination_info_website(
            product_template_id, product_id, combination, add_qty, parent_combination=parent_combination, **kwargs)
