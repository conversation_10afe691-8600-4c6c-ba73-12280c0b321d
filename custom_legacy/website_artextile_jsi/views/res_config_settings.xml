<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="res_config_settings_view_form_website_product_multi_uom" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.website.product.multi.uom</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <block name="ar_custom_setting_container" position="inside">
                <setting help="Warn customer if entered quantity is morethan the allowed quantity">
                    <field name="use_allowed_max_sale_qty"/>
                    <div class="content-group" invisible="not use_allowed_max_sale_qty">
                        <div class="mt16">
                            <label for="allowed_max_sale_qty" class="o_light_label mr8"/>
                            <field name="allowed_max_sale_qty" class="oe_inline"/>
                            <span class="fa fa-lg fa-building-o p-2" title="Values set here are company-specific." groups="base.group_multi_company"/>
                        </div>
                    </div>
                </setting>
                <setting id="stock_qty_threshold" string="Stock Quantity Threshold" help="Show Sold out if on product.qty_total(Free Inv.) is less than the Stock Quantity Threshold">
                    <field name="stock_qty_threshold"/>
                    <span class="fa fa-lg fa-building-o p-2" title="Values set here are company-specific." groups="base.group_multi_company"/>
                </setting>
            </block>
        </field>
    </record>
</odoo>
