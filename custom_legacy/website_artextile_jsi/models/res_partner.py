from odoo import api, fields, models


class ResPartner(models.Model):
    _inherit = "res.partner"

    payment_method_ids = fields.Many2many("payment.method", string="Payment Method")

    @api.model_create_multi
    def create(self, vals_list):
        '''
            OVERRIDE
            - Add default payment Method to the partner
            - If partner is linked with any portal user and buy anything on website, will show these payment methods in the payment page
        '''
        for vals in vals_list:
            vals['payment_method_ids'] = [(6, 0, self.env['payment.method'].search([
                ('default_add_to_partner', '=', True),
                ('active', '=', True)
            ]).ids)]
        return super().create(vals_list)

    def _can_be_edited_by_current_customer(self, sale_order, mode):
        '''OVERRIDE
            Restrict Edit button on Address kanban which are already used
            User can only edit unused address
            There may be ongoing Delivery order, So simply client want to stop user to edit address
        '''
        used_shippings = sale_order.partner_id.sale_order_ids.filtered(lambda x: x.state not in ['draft', 'cencel']).partner_shipping_id
        return self not in used_shippings
