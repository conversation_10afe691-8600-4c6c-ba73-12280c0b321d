from odoo import _, api, fields, models
from odoo.exceptions import UserError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    @api.depends("move_ids_without_package.price_unit", "move_ids_without_package.quantity")
    def _compute_amount_total(self):
        for picking in self:
            picking.amount_total = sum(
                move.price_unit * move.quantity for move in picking.move_ids_without_package
            )

    po_currency_id = fields.Many2one(related="move_ids.purchase_line_id.currency_id")
    amount_total = fields.Monetary(string="Total Amount", store=True, readonly=True, compute="_compute_amount_total", currency_field="po_currency_id")

    def button_validate(self):
        """
        OVERRIDING
        - Auto create invoice and post if delivery order validated successfully.
          It will work only for Fully Transfer, No Backorder
        - Lock sale and purchase on fully validating it's picking
        For validation of delivery order:
        - Quantity should same as the Demand, if not then raise warning and stop operation
        - By pass above warning if it's Admin
        """
        for picking in self:
            if (
                not self.env.user.has_group("base.group_system")
                and picking.picking_type_code == "outgoing"
                and (any(line.product_uom_qty != line.quantity for line in picking.move_ids))
            ):
                raise UserError(_("You CANNOT validate a transfer if no sufficient quantities are reserved nor done."))
        res = super().button_validate()
        # Here if its success, it will return True
        # If there is Immediate Wizard action, it will return action, and on Apply button it will return None
        # If there is a backorder wizard, it will return Action, and then we use use custom context to check
        # Client may validate backorder directly, So, we use backorder_id and for the double security we use backorder_ids
        if res in [True, None]:
            for picking in self:
                if "block_invoice_creation" not in self.env.context and not (
                    picking.backorder_ids or picking.backorder_id
                ):
                    cutting_location = self.env["stock.location"]._get_cutting_location()
                    # Need to do this only for Delivery Order and Source Location is Cutting
                    if picking.picking_type_code == "outgoing" and picking.location_id.id == cutting_location.id:
                        if picking.sale_id:
                            picking.sale_id.action_lock()
                            picking.sale_id._create_invoices()
                            picking.sale_id.invoice_ids.action_post()
                if picking.picking_type_code == "incoming" and picking.purchase_id and picking.purchase_id.is_shipped:
                    picking.purchase_id.button_done()
        return res
