from odoo.upgrade import util


def migrate(cr, version):

    cr.execute("SELECT latest_version FROM ir_module_module WHERE name='base'")
    util.ENVIRON["__base_version"] = util.parse_version(cr.fetchone()[0])

    # remove view
    util.remove_view(cr, "partner_misc_jsi.view_picking_form_partner_misc_jsi")

    # delete fields
    util.remove_field(cr, "stock.move.line", "x_studio_partner")
    util.remove_field(cr, "stock.move", "x_studio_related_field_fPZYw")
    util.remove_field(cr, "account.move", "sale_partner_id")
    util.remove_field(cr, "stock.picking", "sale_partner_id")
