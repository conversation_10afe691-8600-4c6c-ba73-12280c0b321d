<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_move_form_partner_misc_jsi" model="ir.ui.view">
            <field name="name">account.move.form.partner.misc.jsi</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form" />
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes">
                    <attribute name="context">
                        {'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('out_invoice',
                        'out_refund', 'out_receipt') and 'customer') or (context.get('default_move_type', 'entry') in
                        ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False, 'show_address': 1,
                        'default_is_company': True, 'show_vat': True, 'no_parent_name': 1}
                    </attribute>
                </field>
                <field name="journal_id" position="attributes">
                    <attribute name="domain">[('type', '!=', False)]</attribute>
                </field>
            </field>
        </record>
    </data>
</odoo>
