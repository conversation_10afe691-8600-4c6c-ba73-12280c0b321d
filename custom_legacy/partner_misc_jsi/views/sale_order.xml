<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_partner_misc_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.partner.misc.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes">
                    <attribute
                        name="domain"
                    >['|', ('company_id', '=', False), ('company_id', '=', company_id), ('parent_id', '=', False), ('customer_rank','&gt;', 0)]</attribute>
                </field>
                <field name="partner_invoice_id" position="attributes">
                    <attribute name="domain">
                        ['|', ('company_id', '=', False), ('company_id', '=', company_id), ('parent_id', '=',
                        partner_id), ('type', '=', 'invoice')]
                    </attribute>
                    <attribute name="context">{'default_type': 'invoice', 'no_parent_name': 1}</attribute>
                </field>
                <field name="partner_shipping_id" position="attributes">
                    <attribute name="domain">
                        ['|', ('parent_id', '=', partner_id), ('id', '=', partner_id)]
                    </attribute>
                    <attribute name="context">{'default_type': 'delivery', 'no_parent_name': 1}</attribute>
                </field>
                <notebook position="inside">
                    <page string="Child Partner" name="child_partner">
                        <field name="related_child_ids" nolable="1" />
                    </page>
                </notebook>
            </field>
        </record>
    </data>
</odoo>
