<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_partner_form_partner_misc_jsi" model="ir.ui.view">
            <field name="name">res.partner.form.partner.misc.jsi</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form" />
            <field name="arch" type="xml">
                <field name="name" position="before">
                    <field name="customer_rank" invisible="1" />
                </field>
                <span name="address_name" position="before">
                    <field name="code" readonly="1" />
                    <field name="initial" required="customer_rank > 0" invisible="customer_rank == 0" />
                    <field name="short_name" required="customer_rank > 0" />
                    <field name="full_name" required="is_company"/>
                </span>
                <field name="function" position="after">
                    <field name="fax" />
                </field>
                <field name="vat" position="before">
                    <field name="contact" string="Contact" />
                    <field name="company_group_id" invisible="company_type == 'person'" />
                    <field name="initial_user_id" required="customer_rank > 0" invisible="customer_rank == 0" />
                    <field name="subsales_id" required="customer_rank > 0" invisible="customer_rank == 0" />
                    <field name="comment" />
                </field>
                <field name="property_payment_term_id" position="after">
                    <field name="date_join" required="customer_rank > 0" invisible="customer_rank == 0" />
                    <field name="nursing_end_date" />
                    <field name="market_id" required="customer_rank > 0" invisible="customer_rank == 0" />
                    <field name="courier_tracking" />
                </field>
                <xpath expr="//group[@name='sale']/field[@name='user_id']" position="attributes">
                    <attribute name="required">"customer_rank > 0"</attribute>
                    <attribute name="invisible">"customer_rank == 0"</attribute>
                </xpath>
                <field name="property_account_receivable_id" position="attributes">
                    <attribute name="required">"customer_rank > 0"</attribute>
                </field>
                <field name="property_account_payable_id" position="attributes">
                    <attribute name="required">"customer_rank > 0"</attribute>
                </field>
                <page name="internal_notes" position="replace" />
                <group name="misc" position="inside">
                    <field name="lang" position="move" />
                </group>
                <!-- child_ids -->
                <field name="child_ids" position="attributes">
                    <attribute name="context">
                        {'default_parent_id': active_id, 'default_street': street, 'default_street2': street2,
                        'default_city': city, 'default_state_id': state_id, 'default_zip': zip, 'default_country_id':
                        country_id, 'default_lang': lang, 'default_user_id': user_id, 'default_type': 'delivery'}
                    </attribute>
                </field>
                <xpath expr="//field[@name='child_ids']//form//field[@name='comment']" position="attributes">
                    <attribute name="nolabel">0</attribute>
                </xpath>
                <notebook position="inside">
                    <page string="Performance">
                        <field name="perf_chart_ids">
                            <tree>
                                <field name="date_from"/>
                                <field name="date_to"/>
                                <field name="categ_ids" widget="many2many_tags"/>
                                <field name="target"/>
                                <field name="actual_system"/>
                                <field name="manual_adj"/>
                                <field name="actual_display"/>
                                <field name="attainment_rate"/>
                                <field name="message_display"/>
                                <field name="internal_remark"/>
                                <field name="is_display"/>
                                <field name="partner_id" invisible="True"/>
                            </tree>
                            <form>
                                <sheet string="Customer Performance">
                                    <group col="4">
                                        <field name="partner_id"/>
                                        <field name="date_from" style="width: 150px !important;"/>
                                        <field name="date_to"/>
                                        <separator class="perf_form_mt_mb_0"/>
                                        <field name="categ_ids" widget="many2many_tags" colspan="4"/>
                                        <separator class="perf_form_mt_mb_0"/>
                                        <field name="target"/>
                                        <field name="actual_system"/>
                                        <field name="manual_adj"/>
                                        <field name="actual_display"/>
                                        <field name="attainment_rate"/>
                                        <separator class="perf_form_mt_mb_0"/>
                                        <field name="message_display" colspan="4"/>
                                        <separator class="perf_form_mt_mb_0"/>
                                        <field name="internal_remark"/>
                                        <field name="is_display"/>
                                    </group>
                                </sheet>
                            </form>
                        </field>
                    </page>
                </notebook>
            </field>
        </record>

        <record id="res_partner_view_search_partner_misc_jsi" model="ir.ui.view">
            <field name="name">res.partner.view.search.partner.misc.jsi</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='type_company']" position="after">
                    <separator/>
                    <filter string="Performance Chart" name="perf_chart_display" domain="[('perf_chart_ids.is_display', '=', True)]"/>
                    <separator/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
