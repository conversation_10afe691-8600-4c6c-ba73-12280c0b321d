from odoo import fields, models


class ShippingSchedule(models.Model):
    _name = "shipping.schedule"
    _description = "Shipping schedule"

    name = fields.Char("Name")
    vendor_pi_invoice = fields.Char("Vendor PI/Invoice")
    vessel_flight = fields.Char("Vessel/Flight")
    remarks = fields.Text("Remarks")
    eta = fields.Date("ETA")
    etd = fields.Date("ETD")
    rolls = fields.Float("Rolls")
    forwarder_id = fields.Many2one("shipping.forwarder", "Forwarder")
    shipping_mode_id = fields.Many2one("shipping.mode", "Ship mode")
    shipping_status_id = fields.Many2one("shipping.status", "Status")


class ShippingForwarder(models.Model):
    _name = "shipping.forwarder"
    _description = "Shipping Forwarder"

    name = fields.Char("Name")
    description = fields.Char("Description")


class ShippingMode(models.Model):
    _name = "shipping.mode"
    _description = "Shipping Mode"

    name = fields.Char("Name")
    description = fields.Char("Description")


class ShippingStatus(models.Model):
    _name = "shipping.status"
    _description = "Shipping Status"

    name = fields.Char("Name")
    description = fields.Char("Description")
