<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <!-- Shipping Schedule -->
    <record id="view_form_shipping_schedule_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.shipping.schedule.purchase.misc.jsi</field>
        <field name="model">shipping.schedule</field>
        <field name="arch" type="xml">
            <form>
                <sheet string="Shipping Schedules">
                    <div class="oe_title">
                        <h1>
                            <field name="name" required="1" placeholder="Name..." />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field
                                name="shipping_status_id"
                                options="{'no_create': True, 'no_open': True}"
                                required="1"
                            />
                            <field name="shipping_mode_id" options="{'no_create': True, 'no_open': True}" />
                            <field name="forwarder_id" options="{'no_create': True, 'no_open': True}" />
                            <field name="vendor_pi_invoice" />
                            <field name="rolls" />
                        </group>
                        <group>
                            <field name="vessel_flight" />
                            <field name="etd" />
                            <field name="eta" />
                            <field name="remarks" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_tree_shipping_schedule_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.shipping.schedule.purchase.misc.jsi</field>
        <field name="model">shipping.schedule</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree>
                <field name="create_date" />
                <field name="name" />
                <field name="shipping_status_id" widget="badge" />
                <field name="shipping_mode_id" />
                <field name="forwarder_id" />
                <field name="vendor_pi_invoice" />
                <field name="rolls" />
                <field name="vessel_flight" />
                <field name="etd" />
                <field name="eta" />
            </tree>
        </field>
    </record>
    <record id="action_shipping_schedule_purchase_misc_jsi" model="ir.actions.act_window">
        <field name="name">Shipping Schedules</field>
        <field name="res_model">shipping.schedule</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_shipping_schedule_purchase_misc_jsi"
        name="Shipping Schedules"
        action="action_shipping_schedule_purchase_misc_jsi"
        parent="purchase.menu_procurement_management"
    />
    <!-- shipping.forwarder -->
    <record id="view_tree_shipping_forwarder_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.shipping.forwarder.purchase.misc.jsi</field>
        <field name="model">shipping.forwarder</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="name" />
                <field name="description" />
            </tree>
        </field>
    </record>
    <record id="action_shipping_forwarder_purchase_misc_jsi" model="ir.actions.act_window">
        <field name="name">Shipping Forwarder</field>
        <field name="res_model">shipping.forwarder</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_shipping_forwarder_purchase_misc_jsi"
        name="Shipping Forwarder"
        action="action_shipping_forwarder_purchase_misc_jsi"
        parent="purchase.menu_procurement_management"
    />
    <!-- shipping.mode -->
    <record id="view_tree_shipping_mode_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.shipping.mode.purchase.misc.jsi</field>
        <field name="model">shipping.mode</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="name" />
                <field name="description" />
            </tree>
        </field>
    </record>
    <record id="action_shipping_mode_purchase_misc_jsi" model="ir.actions.act_window">
        <field name="name">Shipping Mode</field>
        <field name="res_model">shipping.mode</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_shipping_mode_purchase_misc_jsi"
        name="Shipping Mode"
        action="action_shipping_mode_purchase_misc_jsi"
        parent="purchase.menu_procurement_management"
    />
    <!-- shipping.status -->
    <record id="view_tree_shipping_status_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.shipping.status.purchase.misc.jsi</field>
        <field name="model">shipping.status</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="name" />
                <field name="description" />
            </tree>
        </field>
    </record>
    <record id="action_shipping_status_purchase_misc_jsi" model="ir.actions.act_window">
        <field name="name">Shipping Status</field>
        <field name="res_model">shipping.status</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        id="menu_action_shipping_status_purchase_misc_jsi"
        name="Shipping Status"
        action="action_shipping_status_purchase_misc_jsi"
        parent="purchase.menu_procurement_management"
    />
</odoo>
