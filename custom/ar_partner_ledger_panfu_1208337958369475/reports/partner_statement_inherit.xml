<odoo>
    <template id="report_partner_statement_artextile_jsi_inherit" inherit_id="custom_reports_artextile_jsi.report_partner_statement_artextile_jsi">
        <!-- 调整表头顺序：Invoice Date最前，隐藏Due Date（原模板无Due Date） -->
        <xpath expr="//table[@class='inv_table'][1]/thead/tr" position="replace">
            <tr>
                <th width="25%">Invoice Date</th>
                <th width="25%">Invoice No.</th>
                <th width="25%">Invoice Amt.</th>
                <th>Remaining Amt.</th>
            </tr>
        </xpath>
        <!-- 调整表体顺序 -->
        <xpath expr="//table[@class='inv_table'][1]/tbody/tr[@t-foreach]" position="replace">
            <tr t-foreach="env.context.get('current_preriod_data')" t-as="inv">
                <td><t t-out="inv['invoice_date']"/></td>
                <td><span t-out="inv['name']"/></td>
                <td><span t-out="inv['amount_total']" t-options="widget_float"/></td>
                <td><span t-out="inv['amount_residual']" t-options="widget_float"/></td>
            </tr>
        </xpath>
    </template>
</odoo>