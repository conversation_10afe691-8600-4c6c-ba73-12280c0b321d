- 当前项目的配置文件是 ./odoo.conf, 其中包含了数据库连接方式、addons_path 等；
- 当前Odoo版本为17，遇到模型或模块不存在的问题时，优先用context7查官方/社区文档和Odoo 17源码，不要盲目假设模型名或直接本地搜索。
- 每次在 models 目录下添加新的 model 文件后，都要在 models/__init__.py 中添加对应的导入语句，否则 Odoo 不会自动加载该模型扩展。
- To start the Odoo server in this project, use 'pkill odoo; ./odoo/odoo-bin -c odoo.conf --dev=all' and do not specify the -d database option, as the database is configured in odoo.conf.
- The user prefers that the AI assistant does not automatically restart the project, and instead asks the user to restart it manually.

- The user prefers plugin names in the format 功能_{author}_{asana_task_id}, with the author field set to their name 'panfu'.

- 当遇到odoo技术问题，例如查不到某张表信息、找不到view节点等，都可以利用 MCP Context7 来查询odoo 17文档来参考。

- 任务查询、跟进、更新操作需要用 asana MCP

### 如何操作odoo数据库
当需要操作odoo数据库，使用以下指令执行odoo SQL：
```shell
psql -d $(grep db_name odoo.conf | cut -d= -f2) -U $(grep db_user odoo.conf | cut -d= -f2) -h $(grep db_host odoo.conf | cut -d= -f2) -p $(grep db_port odoo.conf | cut -d= -f2) -c "<SQL>"
```
示例：
```shell
psql -d $(grep db_name odoo.conf | cut -d= -f2) \
     -U $(grep db_user odoo.conf | cut -d= -f2) \
     -h $(grep db_host odoo.conf | cut -d= -f2) \
     -p $(grep db_port odoo.conf | cut -d= -f2) \
     -c "SELECT COUNT(*) FROM res_users WHERE active = true;"
```

### 如何执行执行 odoo shell 命令
当需要执行 odoo shell 命令，可以参考以下方式执行：
```shell
echo "<odoo shell here>" | python3 ./odoo/odoo-bin shell -c odoo.conf -d $(grep db_name odoo.conf | cut -d= -f2 | xargs) --no-http
```
示例：
```shell
python3 ./odoo/odoo-bin shell -c odoo.conf -d $(grep db_name odoo.conf | cut -d= -f2 | xargs)  --no-http << 'EOF'
models = self.env['ir.model'].search([('model', 'like', 'stock.%')])
inventory_models = [m for m in models if 'inventory' in m.model or 'quant' in m.model]
for m in inventory_models:
    print(f'{m.model}: {m.name}')
EOF
```

### 如何使用 Asana MCP:
    第一步：获取工作空间ID

    工具：mcp__asana__asana_list_workspaces
    参数：无必需参数
    可选参数：opt_fields（逗号分隔的字段列表）

    第二步：搜索任务

    工具：mcp__asana__asana_search_tasks
    必需参数：
    - workspace: 工作空间ID (如: 623695457770985)
    - text: 任务名称关键词 (如: "Receipt")

    常用可选参数：
    - assignee_any: 指定负责人ID
    - completed: true/false 筛选完成状态
    - due_on: 指定日期 (YYYY-MM-DD格式)
    - projects_any: 指定项目ID
    - sections_any: 指定分区ID
    - limit: 返回结果数量 (1-100)
    - sort_by: 排序字段 (due_date, created_at, modified_at)

    第三步：获取任务详情

    工具：mcp__asana__asana_get_task
    必需参数：
    - task_id: 任务GID (如: 1210669684218343)

    常用可选参数：
    - opt_fields: 指定返回字段，推荐值：
        "name,completed,assignee,due_on,notes,projects,sections,custom_fields,created_at,modified_at"

    实际查询示例：

    1. 列出工作空间 → 获取workspace_gid
    2. 搜索任务 → 使用workspace + text参数
    3. 获取详情 → 使用任务gid + opt_fields参数
    4. 查看评论获得更多任务的全貌

    常用字段说明：

    - gid: 任务唯一标识符
    - name: 任务名称
    - completed: 完成状态
    - assignee: 负责人
    - due_on: 截止日期
    - custom_fields: 自定义字段状态
    - projects: 所属项目
    - sections: 所在分区


### 开发遵循的工作流
<workflow>
1. 每当我输入新的需求的时候，为了规范需求质量和验收标准，你首先会搞清楚问题和需求
2. 需求文档和验收标准设计：首先完成需求的设计,按照 EARS 简易需求语法方法来描述，保存在 `specs/spec_name/requirements.md` 中，跟我进行确认，最终确认清楚后，需求定稿，参考格式如下

```markdown
# 需求文档

## 介绍

需求描述

## 需求

### 需求 1 - 需求名称

**用户故事：** 用户故事内容

#### 验收标准

1. 采用 ERAS 描述的子句 While <可选前置条件>, when <可选触发器>, the <系统名称> shall <系统响应>，例如 When 选择"静音"时，笔记本电脑应当抑制所有音频输出。
2. ...
...
```
2. 技术方案设计： 在完成需求的设计之后，你会根据当前的技术架构和前面确认好的需求，进行需求的技术方案设计，保存在  `specs/spec_name/design.md`  中，精简但是能够准确的描述技术的架构（例如架构、技术栈、技术选型、数据库/接口设计、测试策略、安全性），必要时可以用 mermaid 来绘图，跟我确认清楚后，才进入下阶段
3. 任务拆分：在完成技术方案设计后，你会根据需求文档和技术方案，细化具体要做的事情，保存在`specs/spec_name/tasks.md` 中, 跟我确认清楚后，才开始正式执行任务，同时更新任务的状态

格式如下

``` markdown
# 实施计划

- [ ] 1. 任务信息
- 具体要做的事情
- ...
- _需求: 相关的需求点的编号

```
</workflow>

