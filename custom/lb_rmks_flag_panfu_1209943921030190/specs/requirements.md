# 需求文档

## 介绍

本需求旨在提升销售订单录入效率和准确性。对于部分客户，需在打印的Label上标记“正”或“反”，该信息需自动体现在销售订单的 LB Rmks 字段。

## 需求

### 需求 1 - 送货地址自动标记“正”或“反”

**用户故事：**
作为销售人员，当我录入销售订单并选择送货地址时，系统应能根据该地址的设定，自动在订单的 LB Rmks 字段最前方添加“正”或“反”，以便后续打印Label时自动带出，无需人工判断和输入。

#### 验收标准

1. While 录入销售订单时，when 选择送货地址，the 系统 shall 检查该地址联系人是否设置了“正/反”标记。
2. When 送货地址联系人设置为“正”或“反”，the 系统 shall 自动在 LB Rmks 字段最前方添加对应的“正”或“反”字样（如原有内容，前方追加，不覆盖原内容）。
3. When 送货地址联系人未设置该标记，the 系统 shall 不自动添加任何内容。
4. When 用户手动修改 LB Rmks 字段，the 系统 shall 允许用户编辑，但如再次切换送货地址，仍按上述规则自动处理。
5. When 联系人界面，the 系统 shall 提供“正/反/null”三选一字段，供维护。 