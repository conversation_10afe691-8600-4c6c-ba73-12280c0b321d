{
    'name': 'PO QTY状态增强 - panfu',
    'version': '********.0',
    'author': 'panfu',
    'category': 'Inventory/Inventory',
    'summary': '扩展PO QTY按钮，支持多状态显示',
    'description': '''
        扩展商品详情页PO QTY按钮（action_view_pending_po_moves），
        使其支持显示["waiting", "confirmed", "partially_available", "assigned"]状态的采购单行，
        满足Asana主任务1210669684218343需求。
    ''',
    'depends': [
        'product_misc_jsi',
        'stock',
    ],
    'sequence': 1000,  # Ensure this loads after product_misc_jsi
    'data': [
        'views/stock_move_views.xml',
    ],
} 