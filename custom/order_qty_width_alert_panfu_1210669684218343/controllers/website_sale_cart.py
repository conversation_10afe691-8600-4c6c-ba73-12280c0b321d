from odoo import http
from odoo.http import request, Response
from odoo.addons.website_sale.controllers.main import WebsiteSale

class WebsiteSaleCartAlert(WebsiteSale):
    @http.route(['/shop/cart/product_info'], type='json', auth='public', website=True)
    def cart_product_info(self, product_id):
        product = request.env['product.product'].sudo().browse(product_id)
        width_cm = product.width_cm or product.product_tmpl_id.width_cm or 0
        return {'width_cm': width_cm}

    @http.route(['/shop/cart/set_ok_remark'], type='json', auth='user', website=True)
    def set_ok_remark(self, order_id):
        order = request.env['sale.order'].sudo().browse(order_id)
        if order:
            field = 'x_studio_so_remarks_1'
            val = getattr(order, field, '')
            if val:
                if not val.startswith('OK'):
                    setattr(order, field, 'OK ' + val)
            else:
                setattr(order, field, 'OK')
        return {'result': True} 