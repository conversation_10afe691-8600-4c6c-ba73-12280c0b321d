from odoo import api, fields, models, _

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    @api.onchange('product_id')
    def _onchange_product_id_width_alert(self):
        if self.product_id and self.product_id.width_cm and self.product_id.width_cm < 145:
            warning = {
                'title': _('窄封提醒'),
                'message': _('窄封，请与客户确认数量OK'),
            }
            return {'warning': warning}
        return {} 