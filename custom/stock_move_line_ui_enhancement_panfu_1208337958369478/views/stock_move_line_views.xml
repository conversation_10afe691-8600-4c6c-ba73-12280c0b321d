<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- ============ 内部调拨（转库）专用视图 ============ -->
    <record id="view_move_line_tree_internal" model="ir.ui.view">
        <field name="name">stock.move.line.tree.internal</field>
        <field name="model">stock.move.line</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <tree string="Internal Transfer Details" create="0" edit="1" decoration-danger="display_warning == True">
                <!-- Hidden required fields -->
                <field name="company_id" column_invisible="True"/>
                <field name="tracking" column_invisible="True"/>
                <field name="move_id" column_invisible="True"/>
                <field name="picking_id" column_invisible="True"/>
                <field name="display_warning" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                
                <!-- Visible fields -->
                <field name="product_id" string="Product" readonly="1"/>
                <field name="x_studio_vendor_item_no" string="Vendor Item No" optional="show"/>
                
                <!-- Optional fields (yellow highlighted - default hidden) -->
                <field name="x_studio_eta" string="ETA" optional="hide"/>
                <field name="x_studio_exmill_date_1" string="Exmill date" optional="hide"/>
                <field name="x_studio_exmill_date" string="Updated Exmill" optional="hide"/>
                
                <!-- Location fields -->
                <field name="location_dest_id" string="To" required="1"/>
                <field name="location_id" string="Pick From" required="1"/>
                
                <!-- Quantity fields -->
                <field name="x_studio_related_field_85n_1irfjfaer" string="Demand" readonly="1"/>
                <field name="quantity" string="Quantity"/>
                <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom"/>
                
                <!-- Available quantity -->
                <field name="lot_qty_available" string="Available Quantity" optional="show"/>
                
                <!-- Warning field - 显示为复选框 -->
                <field name="display_warning" string="Display Warning" optional="show" readonly="1"/>
                
                <!-- Secondary UOM fields (yellow highlighted - default hidden) -->
                <field name="secondary_uom_qty" string="Secondary qty" optional="hide"/>
                <field name="secondary_uom_id" string="Secondary uom" optional="hide"/>
                
                <!-- PO Description (yellow highlighted - default hidden) -->
                <field name="x_studio_po_description" string="PO Description" optional="hide"/>
            </tree>
        </field>
    </record>
    
    <!-- ============ 收货专用视图 ============ -->
    <record id="view_move_line_tree_receipt" model="ir.ui.view">
        <field name="name">stock.move.line.tree.receipt</field>
        <field name="model">stock.move.line</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <tree string="Receipt Details" create="0" edit="1" default_order="product_id,lot_id" decoration-danger="display_warning == True">
                <!-- Hidden required fields -->
                <field name="company_id" column_invisible="True"/>
                <field name="tracking" column_invisible="True"/>
                <field name="move_id" column_invisible="True"/>
                <field name="picking_id" column_invisible="True"/>
                <field name="display_warning" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                
                <!-- Main fields -->
                <field name="product_id" string="Product" readonly="1"/>
                <field name="x_studio_vendor_item_no" string="Vendor Item No" optional="show"/>
                <field name="shipping_sch_id" string="Shipping Schedule" optional="show"/>
                <field name="x_studio_eta" string="ETA" optional="show"/>
                <field name="x_studio_exmill_date" string="Updated Exmill date" optional="show"/>
                <field name="x_studio_exmill_date_1" string="Exmill" optional="show"/>
                
                <!-- Location field -->
                <field name="location_dest_id" string="To" required="1"/>
                
                <!-- Quantity fields -->
                <field name="x_studio_related_field_85n_1irfjfaer" string="Demand" readonly="1"/>
                <field name="gross_unit" string="Gross" optional="show"/>
                <field name="quantity" string="Quantity"/>
                <field name="selvedge_id" string="Selvedge" optional="show"/>
                
                <!-- Lot/Serial field -->
                <field name="lot_id" string="Lot/Serial Number" groups="stock.group_production_lot" context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                
                <!-- Unit of Measure -->
                <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom"/>
                
                <!-- Optional fields (yellow highlighted - default hidden) -->
                <field name="lot_qty_available" string="Available Quantity" optional="hide"/>
                
                <!-- Warning field - 显示为复选框 -->
                <field name="display_warning" string="Display Warning" optional="hide" readonly="1"/>
                
                <field name="secondary_uom_qty" string="Secondary qty" optional="hide"/>
                <field name="secondary_uom_id" string="Secondary uom" optional="hide"/>
                <field name="x_studio_po_description" string="PO Description" optional="hide"/>
            </tree>
        </field>
    </record>
    <record id="stock_move_line_action" model="ir.actions.act_window">
        <field name="name">Moves History</field>
        <field name="res_model">stock.move.line</field>
        <field name="context">{'create': 0, 'pivot_measures': ['quantity_product_uom', '__count__']}</field>
    </record>
</odoo>
