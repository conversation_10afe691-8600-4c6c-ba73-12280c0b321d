from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'
    
    # 重写或创建 display_warning 字段
    display_warning = fields.Boolean(
        string="Display Warning",
        compute='_compute_display_warning',
        store=False,
        readonly=True,  # 设置为只读
        help="Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
    )
    
    # 动态计算help信息的字段（可选，如果需要动态提示）
    display_warning_help = fields.Char(
        string="Warning Help",
        compute='_compute_display_warning',
        store=False
    )
    
    @api.depends('quantity', 'lot_qty_available', 'lot_id')
    def _compute_display_warning(self):
        """计算是否显示警告"""
        for line in self:
            if line.lot_id and line.quantity and line.lot_qty_available:
                # 允许一定的浮点数误差
                if abs(line.quantity - line.lot_qty_available) > 0.01:
                    line.display_warning = True
                    line.display_warning_help = "Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
                else:
                    line.display_warning = False
                    line.display_warning_help = ""
            else:
                line.display_warning = False
                line.display_warning_help = ""
