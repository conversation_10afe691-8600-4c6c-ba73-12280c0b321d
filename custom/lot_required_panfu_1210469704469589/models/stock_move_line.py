from odoo import models, api, _
from odoo.exceptions import ValidationError

class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        records._check_lot_required()
        return records

    def write(self, vals):
        res = super().write(vals)
        self._check_lot_required()
        return res

    def _check_lot_required(self):
        for line in self:
            product = line.product_id
            # Only check for inventory adjustment moves
            if line.move_id and getattr(line.move_id, 'is_inventory', False) and product.tracking in ('lot', 'serial'):
                if not line.lot_id and not line.lot_name:
                    raise ValidationError(_(
                        'Product "%s" is tracked by lot/serial. Lot/Serial Number is required for inventory adjustment.' % product.display_name
                    ))

# class StockInventory(models.Model):
#     _inherit = 'stock.inventory'

#     def action_validate(self):
#         for inventory in self:
#             for line in inventory.line_ids:
#                 product = line.product_id
#                 if product.tracking in ('lot', 'serial'):
#                     if not line.lot_id and not line.lot_name:
#                         raise ValidationError(_(
#                             'Product "%s" is tracked by lot/serial. Lot/Serial Number is required for inventory adjustment.' % product.display_name
#                         ))
#         return super().action_validate() 