from odoo import models, api, fields, _
from odoo.exceptions import ValidationError, UserError

class StockQuant(models.Model):
    _inherit = 'stock.quant'
    
    @api.onchange('inventory_quantity')
    def _onchange_inventory_quantity(self):
        """当修改库存数量时立即检查"""
        if self.product_id.tracking in ('lot', 'serial') and not self.lot_id:
            return {
                'warning': {
                    'title': _('警告'),
                    'message': _('产品"%s"启用了批次/序列号管理，请选择批次号。') % self.product_id.display_name
                }
            }
    
    @api.constrains('inventory_quantity_set', 'lot_id')
    def _check_lot_for_tracked_products(self):
        """约束检查"""
        for quant in self:
            if quant.inventory_quantity_set and quant.product_id.tracking in ('lot', 'serial'):
                if not quant.lot_id:
                    raise ValidationError(_(
                        '产品"%s"启用了批次/序列号管理，必须指定批次/序列号。'
                    ) % quant.product_id.display_name)
    
    def action_apply_inventory(self):
        """应用库存调整时验证"""
        # 检查所有需要批次的产品
        for quant in self:
            if quant.inventory_quantity_set and quant.product_id.tracking in ('lot', 'serial'):
                if not quant.lot_id:
                    raise UserError(_(
                        '产品"%s"启用了批次/序列号管理，必须指定批次/序列号才能应用库存调整。'
                    ) % quant.product_id.display_name)
        
        return super().action_apply_inventory()
