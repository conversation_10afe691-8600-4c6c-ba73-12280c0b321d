# 技术方案设计

## 架构与技术选型
- 基于 Odoo 17 社区版，采用 Python 及 Odoo ORM 框架开发。
- 插件以自定义模块形式集成，命名为 lot_required_panfu_1210469704469589。

## 主要扩展点
- 扩展 `stock.inventory.line` 和/或 `stock.move.line` 模型，增加批号（lot_id/lot_name）必填校验。
- 覆盖盘点单的新增、编辑、保存、验证（action_validate）等操作。
- 扩展批量导入（import）和API接口的校验逻辑。

## 校验实现方式
- 后端：重写/继承 `create`、`write`、`action_validate` 等方法，针对启用批次追踪的产品，若未填写批号则抛出 ValidationError，阻止保存/提交。
- 前端：如有必要，可通过视图XML增加字段required属性，但以后端强制为主，确保所有入口都校验。
- 导入/API：重写`_load_records`等导入相关方法，或在ORM层统一校验，保证批量操作同样受控。

## 关键流程Mermaid图
```mermaid
flowchart TD
    A[用户操作盘点单] --> B{产品是否批次追踪}
    B -- 否 --> C[允许保存/提交]
    B -- 是 --> D{批号是否填写}
    D -- 否 --> E[阻止保存/提交，报错]
    D -- 是 --> F[允许保存/提交]
```

## 测试策略
- 单元测试：覆盖盘点单行的新增、编辑、保存、验证等场景，校验批号必填逻辑。
- 集成测试：模拟批量导入、API操作，确保校验生效。
- 用户测试：实际操作盘点单，验证不同产品类型下的行为。

## 安全性与兼容性
- 仅对启用批次追踪的产品生效，不影响其他产品。
- 兼容Odoo原生及其他自定义插件，避免破坏性更改。

## 其他说明
- 错误提示需明确指出未填写批号的产品或行，便于用户快速定位。 